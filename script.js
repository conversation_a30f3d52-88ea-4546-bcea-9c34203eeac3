// Language Management
let currentLanguage = 'ru';

// Initialize language from localStorage or default to Russian
function initializeLanguage() {
    const savedLanguage = localStorage.getItem('selectedLanguage');
    if (savedLanguage && (savedLanguage === 'ru' || savedLanguage === 'en')) {
        currentLanguage = savedLanguage;
    }
    switchLanguage(currentLanguage);
}

// Switch language function
function switchLanguage(lang) {
    // Проверяем, нужно ли перенаправить на другую страницу
    const currentPath = window.location.pathname;

    if (lang === 'en' && !currentPath.includes('/en/')) {
        window.location.href = './en/index.html';
        return;
    } else if (lang === 'ru' && currentPath.includes('/en/')) {
        window.location.href = '../index.html';
        return;
    }

    // Если уже на нужной странице, обновляем переводы
    if (typeof translations === 'undefined') {
        console.error('Translations object not loaded');
        return;
    }

    if (!translations[lang]) {
        console.error('Language not supported:', lang);
        return;
    }

    currentLanguage = lang;

    // Find all elements with data-translate attribute
    const elements = document.querySelectorAll('[data-translate]');

    // Update text for each element
    elements.forEach(element => {
        const key = element.getAttribute('data-translate');
        const translation = translations && translations[lang] ? translations[lang][key] : null;

        if (translation) {
            // Handle special cases with HTML content
            if (key === 'min_orders.contact') {
                // Keep the link structure for contact elements
                if (element.innerHTML.includes('<a')) {
                    element.innerHTML = translation.replace('@your_betfriend', '<a href="https://t.me/your_betfriend" target="_blank">@your_betfriend</a>');
                } else {
                    element.textContent = translation;
                }
            } else if (key.startsWith('services.webtraffic.feature')) {
                // Handle webtraffic features with bullet points
                element.textContent = translation;
            } else {
                element.textContent = translation;
            }
        } else {
            console.warn('Translation not found for key:', key, 'in language:', lang);
        }
    });

    // Update language switchers
    updateLanguageSwitchers(lang);

    // Save language preference
    localStorage.setItem('selectedLanguage', lang);

    // Update document language attribute
    document.documentElement.lang = lang;
}

// Update language switcher UI
function updateLanguageSwitchers(lang) {
    const allSwitchers = document.querySelectorAll('.language-switcher');

    allSwitchers.forEach(switcher => {
        // Find RU and EN spans more reliably
        const spans = switcher.querySelectorAll('span');
        let ruElement = null;
        let enElement = null;

        spans.forEach(span => {
            if (span.textContent.trim() === 'RU') {
                ruElement = span;
            } else if (span.textContent.trim() === 'EN') {
                enElement = span;
            }
        });

        if (ruElement && enElement) {
            if (lang === 'ru') {
                ruElement.className = 'lang-active';
                enElement.className = 'lang-inactive';
            } else {
                ruElement.className = 'lang-inactive';
                enElement.className = 'lang-active';
            }
        }
    });
}

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize language
    initializeLanguage();
    // Contact button functionality
    const contactBtns = document.querySelectorAll('.contact-btn, .seo-contact-btn, .contact-btn-new');
    contactBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            window.open('https://t.me/your_betfriend', '_blank');
        });
    });

    // Service buttons functionality
    const serviceBtns = document.querySelectorAll('.service-btn');
    const pricingSection = document.querySelector('.pricing');
    const seoSection = document.querySelector('.seo-promotion');

    serviceBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            serviceBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');

            const serviceType = this.getAttribute('data-service');

            if (serviceType === 'followers') {
                pricingSection.style.display = 'block';
                if (seoSection) seoSection.style.display = 'none';
            } else if (serviceType === 'seo') {
                pricingSection.style.display = 'none';
                if (seoSection) seoSection.style.display = 'block';
            }
        });
    });

    // Language switcher functionality
    function setupLanguageSwitchers() {
        const allSwitchers = document.querySelectorAll('.language-switcher');

        allSwitchers.forEach(switcher => {
            const spans = switcher.querySelectorAll('span');

            spans.forEach(span => {
                if (span.textContent.trim() === 'RU' || span.textContent.trim() === 'EN') {
                    span.addEventListener('click', function() {
                        const targetLang = this.textContent.trim() === 'RU' ? 'ru' : 'en';
                        if (targetLang !== currentLanguage) {
                            switchLanguage(targetLang);
                        }
                    });

                    // Add cursor pointer style
                    span.style.cursor = 'pointer';
                }
            });
        });
    }

    // Setup language switchers
    setupLanguageSwitchers();

    // Smooth scrolling for internal links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    const tableRows = document.querySelectorAll('.table-row');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#e3f2fd';
        });
        
        row.addEventListener('mouseleave', function() {
            if (this.matches(':nth-child(even)')) {
                this.style.backgroundColor = '#f8f9fa';
            } else {
                this.style.backgroundColor = '';
            }
        });
    });

    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    const statItems = document.querySelectorAll('.stat-item');
    statItems.forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(item);
    });


});

window.addEventListener('load', function() {
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';
    
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});
