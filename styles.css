/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background-color: #ffffff;
    border-bottom: 1px solid #e9ecef;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.header-desktop {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.header-mobile {
    display: none;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-img {
    height: 40px;
    width: auto;
    color: #1a1a1a;
}

.logo-slogan {
    font-size: 14px;
    color: #6b7280;
    font-weight: 400;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.language-switcher {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
}

.lang-active {
    color: #6366f1;
    font-weight: 600;
}

.lang-inactive {
    color: #6c757d;
    cursor: pointer;
    transition: color 0.3s ease;
}

.lang-inactive:hover {
    color: #6366f1;
}

.lang-separator {
    color: #6c757d;
}

/* Base button styles */
.btn-base {
    color: white;
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-base:hover {
    transform: translateY(-2px);
}

.contact-btn {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
}

.contact-btn:hover {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    box-shadow: 0 8px 24px rgba(99, 102, 241, 0.3);
}

/* Main Content */
.main {
    padding: 40px 0;
}

/* Hero Section */
.hero {
    text-align: center;
    margin-bottom: 60px;
}

.hero-title {
    font-size: 48px;
    font-weight: 700;
    color: #212529;
    margin-bottom: 20px;
}

.hero-subtitle {
    font-size: 22px;
    color: #6c757d;
    margin-bottom: 40px;
    white-space: nowrap;
    text-align: center;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-bottom: 40px;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
}

.stat-item {
    text-align: center;
    padding: 15px 10px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-number {
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 4px;
}

.stat-platform {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
}

/* Service Buttons - Toggle Slider */
.services-buttons {
    display: flex;
    justify-content: center;
    margin: 0 auto;
    max-width: 600px;
    background: #e9ecef;
    border-radius: 25px;
    padding: 4px;
    position: relative;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.service-btn {
    flex: 1;
    padding: 12px 32px;
    border: none;
    background: transparent;
    color: #6c757d;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    z-index: 2;
}

.service-btn:first-child {
    border-radius: 21px 5px 5px 21px;
}

.service-btn:last-child {
    border-radius: 5px 21px 21px 5px;
}

.service-btn.active {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    color: white;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
}

.service-btn:not(.active):hover {
    color: #495057;
    background: rgba(255, 255, 255, 0.5);
}

/* Pricing Section */
.pricing {
    margin-top: 40px;
}

.pricing-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 30px;
    color: #1a1a1a;
}

/* Platform Container */
.platform-container {
    margin-bottom: 30px;
    background: #f8f9fa;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Platform Header */
.platform-header {
    padding: 16px 24px;
    color: white;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
}

.platform-header.telegram {
    background: linear-gradient(135deg, #0088cc, #229ed9);
}

.platform-header.instagram {
    background: linear-gradient(135deg, #833ab4, #fd1d1d, #fcb045);
}

.platform-header.tiktok {
    background: linear-gradient(135deg, #000000, #ff0050);
}

.platform-header.twitter {
    background: linear-gradient(135deg, #1da1f2, #0d8bd9);
}

.platform-header.webtraffic {
    background: linear-gradient(135deg, #10b981, #059669);
}

.platform-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

/* Account Section */
.account-section {
    background: white;
    margin: 0;
}

.account-section.regular {
    background: #f8f9fa;
}

.account-section.premium {
    background: #fff8e1;
}

/* Account Type Header */
.account-type-header {
    background: #e3f2fd;
    color: #6366f1;
    font-size: 16px;
    font-weight: 600;
    padding: 12px 24px;
    margin: 0;
}

.account-section.premium .account-type-header {
    background: #fff3cd;
    color: #b8860b;
}

/* Account Subtitle */
.account-subtitle {
    font-size: 14px;
    color: #6c757d;
    text-align: left;
    padding: 12px 24px;
    margin: 0;
    font-style: italic;
    background: #f8f9fa;
}

/* Pricing Tables */
.pricing-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
    background: white;
}

.table-header {
    background: #f8f9fa;
}

.table-header th {
    padding: 12px 16px;
    font-size: 15px;
    font-weight: 700;
    color: #343a40 !important;
    text-align: left;
    border-bottom: 2px solid #dee2e6;
}

.table-header th:nth-child(3) {
    text-align: right;
    color: #343a40 !important;
}

.table-row {
    border-bottom: 1px solid #dee2e6;
    background: white !important;
}

.table-row:nth-child(even) {
    background: white !important;
}

.table-row:nth-child(odd) {
    background: white !important;
}

.table-row:last-child {
    border-bottom: none;
}

.table-cell {
    padding: 12px 16px;
    font-size: 14px;
    color: #495057;
    vertical-align: top;
    text-align: left;
    font-weight: 600;
    background: white !important;
    box-sizing: border-box;
}

.table-cell:nth-child(1) {
    max-width: 50%;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

.table-cell:nth-child(2) {
    min-width: 200px;
    max-width: 35%;
    white-space: nowrap;
}

.table-cell:nth-child(3) {
    min-width: 120px;
    max-width: 15%;
    white-space: normal;
    word-wrap: break-word;
    overflow-wrap: break-word;
    text-align: right;
    color: #6c757d;
    font-weight: 600;
}



.table-cell a {
    color: #6366f1;
    text-decoration: none;
}

.table-cell a:hover {
    text-decoration: underline;
}

/* Contact Section */
.contact-section {
    text-align: center;
    margin-top: 50px;
    padding: 40px 0;
}

.contact-btn-new {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    padding: 15px 40px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.contact-btn-new:hover {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    box-shadow: 0 8px 24px rgba(99, 102, 241, 0.3);
}

.contact-info {
    font-size: 16px;
    margin: 0;
    color: #6b7280;
}

.contact-info a {
    color: #6366f1;
    text-decoration: none;
    font-weight: 600;
}

.contact-info a:hover {
    text-decoration: underline;
}

/* Footer */
.footer {
    background-color: #212529;
    color: white;
    padding: 40px 0 20px;
    margin-top: 60px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-brand {
    font-size: 18px;
    font-weight: 700;
}

.footer-links {
    display: flex;
    gap: 20px;
}

.footer-link {
    color: #adb5bd;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: white;
}

.footer-copyright {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #495057;
}

.footer-copyright p {
    font-size: 14px;
    color: #adb5bd;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-desktop {
        display: none;
    }

    .header-mobile {
        display: flex;
        flex-direction: column;
        gap: 8px;
        width: 100%;
    }

    .header-content {
        padding: 20px 0;
    }

    .header-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    .header-bottom {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        width: 100%;
        margin-top: 2px;
    }

    .logo {
        flex-direction: row;
        gap: 8px;
        align-items: center;
        justify-content: flex-start;
    }

    .logo-slogan {
        font-size: 12px;
        color: #6b7280;
        font-weight: 400;
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .header-right {
        display: flex;
        flex-direction: row;
        gap: 12px;
        align-items: center;
    }

    .contact-btn {
        padding: 8px 16px;
        font-size: 13px;
        border-radius: 5px;
    }
    
    .hero-title {
        font-size: 36px;
    }

    .hero-subtitle {
        font-size: 18px;
        white-space: normal;
    }

    .service-btn {
        padding: 10px 24px;
        font-size: 14px;
    }

    .services-buttons {
        max-width: 95%;
        margin: 0 auto;
        padding: 4px;
        flex-direction: row;
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        max-width: 100%;
    }

    /* Table wrapper for horizontal scroll on tablets */
    .table-wrapper {
        overflow-x: auto;
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
        width: 100%;
    }

    .pricing-table {
        min-width: 650px;
    }

    .table-header,
    .table-row {
        grid-template-columns: 1fr;
    }
    
    .table-cell {
        border-right: none;
        border-bottom: 1px solid #dee2e6;
    }
    
    .table-cell:last-child {
        border-bottom: none;
    }

    .table-header {
        position: relative;
        border-bottom: 2px solid #dee2e6;
    }

    .table-header th {
        border-bottom: none;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .footer-links {
        justify-content: center;
    }

    .pricing-title {
        font-size: 2rem;
    }

    .platform-header {
        font-size: 18px;
        padding: 12px 16px;
        gap: 10px;
    }

    .platform-icon {
        width: 20px;
        height: 20px;
    }

    /* SEO Mobile Styles */
    .seo-title {
        font-size: 2rem;
    }

    .seo-container {
        margin: 0 10px;
    }

    .seo-container-content {
        padding: 15px;
    }

    .seo-banner {
        padding: 12px 16px;
        font-size: 18px;
        gap: 10px;
    }

    .seo-banner-text {
        font-size: 1rem;
    }

    .seo-description {
        font-size: 1rem;
        margin-bottom: 40px;
        padding: 0 10px;
    }

    .seo-features {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-bottom: 40px;
    }

    .seo-feature {
        padding: 15px;
        min-height: 80px;
        display: flex;
        align-items: center;
    }

    .seo-feature-icon {
        width: 40px;
        height: 40px;
    }

    .seo-feature-icon svg {
        width: 20px;
        height: 20px;
    }

    .seo-feature-text {
        font-size: 0.9rem;
    }

    .seo-pricing {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-bottom: 40px;
    }

    .seo-card {
        padding: 15px;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        overflow: hidden;
        min-height: 280px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .seo-card-icon {
        width: 50px;
        height: 50px;
    }

    .seo-card-icon svg {
        width: 28px;
        height: 28px;
    }

    .seo-card-title {
        font-size: 1.3rem;
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
    }

    .seo-card-price {
        font-size: 1.8rem;
    }

    .seo-contact-btn {
        padding: 12px 30px;
        font-size: 1rem;
    }

    .contact-btn-new {
        padding: 12px 30px;
        font-size: 1rem;
    }

    .seo-card-description {
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
        font-size: 0.9rem;
    }

    .seo-card-features {
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    .seo-card-features li {
        font-size: 0.85rem;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    /* Table wrapper for horizontal scroll */
    .table-wrapper {
        overflow-x: auto;
        scroll-behavior: smooth;
        margin: 0;
        padding: 0;
        width: 100%;
        -webkit-overflow-scrolling: touch;
    }

    .pricing-table {
        min-width: 650px;
        margin: 0;
    }
    
    .hero-title {
        font-size: 28px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
    
    .stat-number {
        font-size: 28px;
    }

    .table-cell {
        padding: 10px;
        font-size: 13px;
    }

    .table-cell:nth-child(1) {
        min-width: 200px;
        width: 40%;
        font-size: 11px;
    }

    .table-cell:nth-child(2) {
        min-width: 250px;
        width: 45%;
        font-size: 11px;
    }

    .table-cell:nth-child(3) {
        min-width: 120px;
        width: 15%;
        font-size: 10px;
        line-height: 1.2;
        white-space: nowrap;
        padding-right: 15px; /* Добавляем отступ справа для последнего столбца */
    }

    /* Дополнительные стили для улучшения скролла на мобильных */
    .table-wrapper::-webkit-scrollbar {
        height: 4px;
    }

    .table-wrapper::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
    }

    .table-wrapper::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
    }

    .table-wrapper::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }
}

/* SEO Promotion Section */
.seo-promotion {
    margin-top: 40px;
}

.seo-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 30px;
    color: #1a1a1a;
}

.seo-container {
    background: #f8f9fa;
    border-radius: 16px;
    margin: 0 auto;
    max-width: 1200px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.seo-banner {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #10b981, #3b82f6);
    color: white;
    padding: 16px 24px;
    margin: 0;
    gap: 12px;
    width: 100%;
    border-radius: 0;
    font-size: 18px;
    font-weight: 600;
}

.seo-banner-icon {
    width: 20px;
    height: 20px;
}

.seo-banner-icon svg {
    width: 100%;
    height: 100%;
}

.seo-banner-text {
    font-size: 1.1rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.seo-container-content {
    padding: 40px;
    background: white;
}

.seo-description {
    font-size: 1.1rem;
    color: #6b7280;
    margin-bottom: 50px;
    max-width: 600px;
}

/* SEO Features Grid */
.seo-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.seo-feature {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.seo-feature:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.seo-feature-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.seo-feature-icon svg {
    width: 24px;
    height: 24px;
    color: white;
}

.seo-feature-icon.target {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
}

.seo-feature-icon.growth {
    background: linear-gradient(135deg, #10b981, #059669);
}

.seo-feature-icon.keywords {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.seo-feature-icon.analytics {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.seo-feature-icon.manager {
    background: linear-gradient(135deg, #6366f1, #4f46e5);
}

.seo-feature-icon.speed {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.seo-feature-text {
    font-size: 1rem;
    font-weight: 500;
    color: #374151;
    line-height: 1.4;
}

/* SEO Pricing Cards */
.seo-pricing {
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
    margin-bottom: 50px;
}

@media (min-width: 769px) {
    .seo-pricing {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    }
}

.seo-card {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.seo-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.seo-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.telegram-card::before {
    background: linear-gradient(135deg, #0088cc, #229ed9);
}

.google-card::before {
    background: linear-gradient(135deg, #10b981, #059669);
}

.seo-card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.telegram-card .seo-card-icon {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
}

.telegram-card .seo-card-icon svg {
    color: #0088cc;
    width: 32px;
    height: 32px;
}

.google-card .seo-card-icon {
    background: linear-gradient(135deg, #ecfdf5, #d1fae5);
}

.google-card .seo-card-icon svg {
    color: #10b981;
    width: 32px;
    height: 32px;
}

.seo-card-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #1a1a1a;
}

.seo-card-price {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.telegram-card .seo-card-price {
    color: #0088cc;
}

.google-card .seo-card-price {
    color: #10b981;
}

.seo-card-description {
    color: #6b7280;
    margin-bottom: 20px;
    line-height: 1.5;
}

.seo-card-features {
    list-style: none;
    padding: 0;
}

.seo-card-features li {
    padding: 8px 0;
    color: #374151;
    font-size: 0.95rem;
}

.seo-card-features li:not(:last-child) {
    border-bottom: 1px solid #f3f4f6;
}

/* SEO Contact Section */
.seo-contact {
    text-align: center;
    padding: 40px 0;
}

.seo-contact-btn {
    background: linear-gradient(135deg, #10b981, #059669);
    padding: 15px 40px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.seo-contact-btn:hover {
    background: linear-gradient(135deg, #059669, #047857);
    box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
}

.seo-contact-info {
    color: #6b7280;
    font-size: 1rem;
}

.seo-contact-info a {
    color: #10b981;
    text-decoration: none;
    font-weight: 500;
}

.seo-contact-info a:hover {
    text-decoration: underline;
}